# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_PATH=./data/testing-tool.db
DB_BACKUP_PATH=./data/backups

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# File Upload Configuration
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=.xlsx,.xls,.csv,.json

# Redis Configuration (for queues and caching)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Frontend Configuration
FRONTEND_URL=http://localhost:5173

# Test Execution Configuration
MAX_PARALLEL_TESTS=5
TEST_TIMEOUT=300000
SCREENSHOT_PATH=./screenshots
VIDEO_PATH=./videos

# Reporting Configuration
REPORT_PATH=./reports
REPORT_RETENTION_DAYS=30

# Security Configuration
CORS_ORIGIN=http://localhost:5173
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# Logging Configuration
LOG_LEVEL=info
LOG_PATH=./logs
