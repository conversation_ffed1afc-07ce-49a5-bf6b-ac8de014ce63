const path = require("path");
const fs = require("fs");

const dbPath =
  process.env.DB_PATH || path.join(__dirname, "../../data/testing-tool.json");

// Ensure data directory exists
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Simple JSON database
const defaultData = {
  users: [],
  test_projects: [],
  test_suites: [],
  test_cases: [],
  test_data_sets: [],
  test_executions: [],
  test_results: [],
  reports: [],
};

let dbData = defaultData;

// Load data from file
function loadData() {
  try {
    if (fs.existsSync(dbPath)) {
      const fileContent = fs.readFileSync(dbPath, "utf8");
      dbData = JSON.parse(fileContent);
    }
  } catch (error) {
    console.error("Error loading database:", error);
    dbData = defaultData;
  }
}

// Save data to file
function saveData() {
  try {
    fs.writeFileSync(dbPath, JSON.stringify(dbData, null, 2));
  } catch (error) {
    console.error("Error saving database:", error);
  }
}

// Simple database helper
const createQueryHelper = (tableName) => {
  return {
    where: (conditions) => {
      return {
        first: () => {
          loadData();
          const records = dbData[tableName] || [];
          const result =
            records.find((record) => {
              return Object.keys(conditions).every(
                (key) => record[key] === conditions[key]
              );
            }) || null;
          console.log(
            `Database query for ${tableName} with conditions:`,
            conditions,
            "Result:",
            result
          );
          return result;
        },
        update: (updateData) => {
          loadData();
          const records = dbData[tableName] || [];
          const index = records.findIndex((record) => {
            return Object.keys(conditions).every(
              (key) => record[key] === conditions[key]
            );
          });

          if (index !== -1) {
            records[index] = {
              ...records[index],
              ...updateData,
              updated_at: new Date().toISOString(),
            };
            saveData();
            return 1;
          }
          return 0;
        },
        del: () => {
          loadData();
          const records = dbData[tableName] || [];
          const initialLength = records.length;
          dbData[tableName] = records.filter((record) => {
            return !Object.keys(conditions).every(
              (key) => record[key] === conditions[key]
            );
          });
          saveData();
          return initialLength - dbData[tableName].length;
        },
      };
    },
    insert: (data) => {
      loadData();
      if (!dbData[tableName]) {
        dbData[tableName] = [];
      }

      const record = {
        id: Date.now() + Math.random(),
        ...data,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      dbData[tableName].push(record);
      saveData();
      return [record.id];
    },
  };
};

// Create table helpers
const dbHelper = (tableName) => {
  const helper = createQueryHelper(tableName);

  // Add a direct where method that returns a promise
  helper.where = (conditions) => {
    return {
      first: async () => {
        loadData();
        const records = dbData[tableName] || [];
        return (
          records.find((record) => {
            return Object.keys(conditions).every(
              (key) => record[key] === conditions[key]
            );
          }) || null
        );
      },
      update: async (updateData) => {
        loadData();
        const records = dbData[tableName] || [];
        const index = records.findIndex((record) => {
          return Object.keys(conditions).every(
            (key) => record[key] === conditions[key]
          );
        });

        if (index !== -1) {
          records[index] = {
            ...records[index],
            ...updateData,
            updated_at: new Date().toISOString(),
          };
          saveData();
          return 1;
        }
        return 0;
      },
      del: async () => {
        loadData();
        const records = dbData[tableName] || [];
        const initialLength = records.length;
        dbData[tableName] = records.filter((record) => {
          return !Object.keys(conditions).every(
            (key) => record[key] === conditions[key]
          );
        });
        saveData();
        return initialLength - dbData[tableName].length;
      },
    };
  };

  return helper;
};

// Database initialization
async function initializeDatabase() {
  try {
    // Load existing data or create new file
    loadData();

    // Initialize with default structure if empty
    if (!dbData || Object.keys(dbData).length === 0) {
      dbData = defaultData;
      saveData();
    }

    console.log("Database initialized successfully");
    return dbHelper;
  } catch (error) {
    console.error("Database initialization failed:", error);
    throw error;
  }
}

module.exports = {
  db: dbHelper,
  initializeDatabase,
};
