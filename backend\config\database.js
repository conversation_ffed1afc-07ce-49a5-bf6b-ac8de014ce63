const knex = require('knex');
const path = require('path');
const fs = require('fs').promises;

const dbPath = process.env.DB_PATH || path.join(__dirname, '../../data/testing-tool.db');

// Knex configuration
const knexConfig = {
  client: 'sqlite3',
  connection: {
    filename: dbPath
  },
  useNullAsDefault: true,
  migrations: {
    directory: path.join(__dirname, '../migrations')
  },
  seeds: {
    directory: path.join(__dirname, '../seeds')
  }
};

const db = knex(knexConfig);

// Database schema creation
async function createTables() {
  // Users table
  await db.schema.createTableIfNotExists('users', (table) => {
    table.increments('id').primary();
    table.string('username').unique().notNullable();
    table.string('email').unique().notNullable();
    table.string('password_hash').notNullable();
    table.string('first_name');
    table.string('last_name');
    table.enum('role', ['admin', 'manager', 'tester', 'viewer']).defaultTo('tester');
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);
  });

  // Test Projects table
  await db.schema.createTableIfNotExists('test_projects', (table) => {
    table.increments('id').primary();
    table.string('name').notNullable();
    table.text('description');
    table.integer('created_by').references('id').inTable('users');
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);
  });

  // Test Suites table
  await db.schema.createTableIfNotExists('test_suites', (table) => {
    table.increments('id').primary();
    table.string('name').notNullable();
    table.text('description');
    table.integer('project_id').references('id').inTable('test_projects').onDelete('CASCADE');
    table.integer('created_by').references('id').inTable('users');
    table.boolean('is_active').defaultTo(true);
    table.timestamps(true, true);
  });

  // Test Cases table
  await db.schema.createTableIfNotExists('test_cases', (table) => {
    table.increments('id').primary();
    table.string('title').notNullable();
    table.text('description');
    table.text('preconditions');
    table.text('test_steps');
    table.text('expected_result');
    table.enum('type', ['unit', 'integration', 'functional', 'api', 'ui', 'performance']).defaultTo('functional');
    table.enum('priority', ['low', 'medium', 'high', 'critical']).defaultTo('medium');
    table.enum('status', ['draft', 'active', 'deprecated']).defaultTo('draft');
    table.integer('suite_id').references('id').inTable('test_suites').onDelete('CASCADE');
    table.integer('created_by').references('id').inTable('users');
    table.json('tags');
    table.json('custom_fields');
    table.timestamps(true, true);
  });

  // Test Data Sets table
  await db.schema.createTableIfNotExists('test_data_sets', (table) => {
    table.increments('id').primary();
    table.string('name').notNullable();
    table.text('description');
    table.enum('type', ['csv', 'excel', 'json', 'manual']).defaultTo('manual');
    table.text('file_path');
    table.json('schema');
    table.json('data');
    table.integer('project_id').references('id').inTable('test_projects').onDelete('CASCADE');
    table.integer('created_by').references('id').inTable('users');
    table.timestamps(true, true);
  });

  // Test Executions table
  await db.schema.createTableIfNotExists('test_executions', (table) => {
    table.increments('id').primary();
    table.string('name').notNullable();
    table.text('description');
    table.enum('status', ['pending', 'running', 'completed', 'failed', 'cancelled']).defaultTo('pending');
    table.integer('suite_id').references('id').inTable('test_suites').onDelete('CASCADE');
    table.integer('executed_by').references('id').inTable('users');
    table.datetime('started_at');
    table.datetime('completed_at');
    table.json('configuration');
    table.json('results');
    table.timestamps(true, true);
  });

  // Test Results table
  await db.schema.createTableIfNotExists('test_results', (table) => {
    table.increments('id').primary();
    table.integer('execution_id').references('id').inTable('test_executions').onDelete('CASCADE');
    table.integer('test_case_id').references('id').inTable('test_cases').onDelete('CASCADE');
    table.enum('status', ['pass', 'fail', 'skip', 'error']).notNullable();
    table.text('error_message');
    table.text('logs');
    table.text('screenshot_path');
    table.text('video_path');
    table.integer('duration_ms');
    table.datetime('executed_at');
    table.json('metadata');
    table.timestamps(true, true);
  });

  // Reports table
  await db.schema.createTableIfNotExists('reports', (table) => {
    table.increments('id').primary();
    table.string('name').notNullable();
    table.enum('type', ['execution', 'coverage', 'trend', 'custom']).defaultTo('execution');
    table.text('description');
    table.json('filters');
    table.text('file_path');
    table.enum('format', ['html', 'pdf', 'excel', 'json']).defaultTo('html');
    table.integer('generated_by').references('id').inTable('users');
    table.datetime('generated_at');
    table.timestamps(true, true);
  });

  console.log('Database tables created successfully');
}

async function initializeDatabase() {
  try {
    // Ensure data directory exists
    const dataDir = path.dirname(dbPath);
    await fs.mkdir(dataDir, { recursive: true });
    
    // Create tables
    await createTables();
    
    console.log('Database initialized successfully');
    return db;
  } catch (error) {
    console.error('Database initialization failed:', error);
    throw error;
  }
}

module.exports = {
  db,
  initializeDatabase,
  knexConfig
};
