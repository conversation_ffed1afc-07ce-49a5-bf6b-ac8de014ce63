const { Low } = require("lowdb");
const { JSONFile } = require("lowdb/node");
const path = require("path");
const fs = require("fs");

const dbPath =
  process.env.DB_PATH || path.join(__dirname, "../../data/testing-tool.json");

// Ensure data directory exists
const dataDir = path.dirname(dbPath);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// Initialize LowDB
const adapter = new JSONFile(dbPath);
const defaultData = {
  users: [],
  test_projects: [],
  test_suites: [],
  test_cases: [],
  test_data_sets: [],
  test_executions: [],
  test_results: [],
  reports: [],
};

const db = new Low(adapter, defaultData);

// Simple query helpers to mimic Knex API
const createQueryHelper = (tableName) => {
  return {
    select: (fields = "*") => {
      return {
        where: (conditions) => {
          const records = db.data[tableName] || [];
          if (typeof conditions === "object") {
            return records.filter((record) => {
              return Object.keys(conditions).every(
                (key) => record[key] === conditions[key]
              );
            });
          }
          return records;
        },
        first: () => {
          const records = db.data[tableName] || [];
          return records[0] || null;
        },
        orderBy: (field, direction = "asc") => {
          return {
            limit: (count) => {
              return {
                offset: (skip) => {
                  const records = db.data[tableName] || [];
                  const sorted = records.sort((a, b) => {
                    if (direction === "desc") {
                      return b[field] > a[field] ? 1 : -1;
                    }
                    return a[field] > b[field] ? 1 : -1;
                  });
                  return sorted.slice(skip, skip + count);
                },
              };
            },
          };
        },
      };
    },
    insert: async (data) => {
      await db.read();
      if (!db.data[tableName]) {
        db.data[tableName] = [];
      }

      const record = {
        id: Date.now() + Math.random(), // Simple ID generation
        ...data,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      db.data[tableName].push(record);
      await db.write();
      return [record.id];
    },
    where: (conditions) => {
      return {
        first: async () => {
          await db.read();
          const records = db.data[tableName] || [];
          return (
            records.find((record) => {
              return Object.keys(conditions).every(
                (key) => record[key] === conditions[key]
              );
            }) || null
          );
        },
        update: async (updateData) => {
          await db.read();
          const records = db.data[tableName] || [];
          const index = records.findIndex((record) => {
            return Object.keys(conditions).every(
              (key) => record[key] === conditions[key]
            );
          });

          if (index !== -1) {
            records[index] = {
              ...records[index],
              ...updateData,
              updated_at: new Date().toISOString(),
            };
            await db.write();
            return 1;
          }
          return 0;
        },
        del: async () => {
          await db.read();
          const records = db.data[tableName] || [];
          const initialLength = records.length;
          db.data[tableName] = records.filter((record) => {
            return !Object.keys(conditions).every(
              (key) => record[key] === conditions[key]
            );
          });
          await db.write();
          return initialLength - db.data[tableName].length;
        },
      };
    },
  };
};

// Create table helpers
const dbHelper = (tableName) => createQueryHelper(tableName);

// Database initialization
async function initializeDatabase() {
  try {
    // Read the database to ensure it's initialized
    await db.read();

    // Initialize with default structure if empty
    if (!db.data) {
      db.data = defaultData;
      await db.write();
    }

    console.log("Database initialized successfully");
    return dbHelper;
  } catch (error) {
    console.error("Database initialization failed:", error);
    throw error;
  }
}

module.exports = {
  db: dbHelper,
  initializeDatabase,
};
