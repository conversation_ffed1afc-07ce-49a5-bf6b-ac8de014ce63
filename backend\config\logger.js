const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logsDir = process.env.LOG_PATH || path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
      msg += ` ${JSON.stringify(meta)}`;
    }
    return msg;
  })
);

// Create logger instance
function setupLogger() {
  const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat,
    defaultMeta: { service: 'testing-tool-backend' },
    transports: [
      // Write all logs with level 'error' and below to error.log
      new winston.transports.File({
        filename: path.join(logsDir, 'error.log'),
        level: 'error',
        maxsize: 5242880, // 5MB
        maxFiles: 5,
        tailable: true
      }),
      
      // Write all logs with level 'info' and below to combined.log
      new winston.transports.File({
        filename: path.join(logsDir, 'combined.log'),
        maxsize: 5242880, // 5MB
        maxFiles: 5,
        tailable: true
      }),
      
      // Write all logs with level 'debug' and below to debug.log
      new winston.transports.File({
        filename: path.join(logsDir, 'debug.log'),
        level: 'debug',
        maxsize: 5242880, // 5MB
        maxFiles: 3,
        tailable: true
      })
    ],
    
    // Handle exceptions and rejections
    exceptionHandlers: [
      new winston.transports.File({
        filename: path.join(logsDir, 'exceptions.log')
      })
    ],
    
    rejectionHandlers: [
      new winston.transports.File({
        filename: path.join(logsDir, 'rejections.log')
      })
    ]
  });

  // Add console transport for development
  if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
      format: consoleFormat
    }));
  }

  // Add test execution specific logger
  const testLogger = winston.createLogger({
    level: 'info',
    format: logFormat,
    defaultMeta: { service: 'test-execution' },
    transports: [
      new winston.transports.File({
        filename: path.join(logsDir, 'test-execution.log'),
        maxsize: 10485760, // 10MB
        maxFiles: 10,
        tailable: true
      })
    ]
  });

  // Add API access logger
  const apiLogger = winston.createLogger({
    level: 'info',
    format: logFormat,
    defaultMeta: { service: 'api-access' },
    transports: [
      new winston.transports.File({
        filename: path.join(logsDir, 'api-access.log'),
        maxsize: 5242880, // 5MB
        maxFiles: 7,
        tailable: true
      })
    ]
  });

  // Attach additional loggers to main logger
  logger.testLogger = testLogger;
  logger.apiLogger = apiLogger;

  return logger;
}

// Log cleanup function
function cleanupOldLogs() {
  const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
  const now = Date.now();

  try {
    const files = fs.readdirSync(logsDir);
    files.forEach(file => {
      const filePath = path.join(logsDir, file);
      const stats = fs.statSync(filePath);
      
      if (now - stats.mtime.getTime() > maxAge) {
        fs.unlinkSync(filePath);
        console.log(`Deleted old log file: ${file}`);
      }
    });
  } catch (error) {
    console.error('Error cleaning up old logs:', error);
  }
}

// Schedule log cleanup (run daily)
setInterval(cleanupOldLogs, 24 * 60 * 60 * 1000);

module.exports = {
  setupLogger,
  cleanupOldLogs
};
