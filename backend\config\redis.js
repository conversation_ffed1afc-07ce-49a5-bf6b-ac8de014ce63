const redis = require("redis");
const Bull = require("bull");

let redisClient;
let testExecutionQueue;

async function setupRedis() {
  try {
    // Skip Redis setup if not available (for development)
    if (process.env.NODE_ENV === "development" && !process.env.REDIS_URL) {
      console.log("Redis disabled for development mode");
      return;
    }

    // Create Redis client
    redisClient = redis.createClient({
      url: process.env.REDIS_URL || "redis://localhost:6379",
      password: process.env.REDIS_PASSWORD || undefined,
      retry_strategy: (options) => {
        if (options.error && options.error.code === "ECONNREFUSED") {
          console.log("Redis server connection refused");
          return new Error("Redis server connection refused");
        }
        if (options.total_retry_time > 1000 * 60 * 60) {
          return new Error("Retry time exhausted");
        }
        if (options.attempt > 10) {
          return undefined;
        }
        return Math.min(options.attempt * 100, 3000);
      },
    });

    redisClient.on("error", (err) => {
      console.error("Redis Client Error:", err);
    });

    redisClient.on("connect", () => {
      console.log("Connected to Redis");
    });

    await redisClient.connect();

    // Setup Bull queues for background job processing
    testExecutionQueue = new Bull("test execution", {
      redis: {
        host: process.env.REDIS_HOST || "localhost",
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD || undefined,
      },
    });

    // Queue event handlers
    testExecutionQueue.on("completed", (job, result) => {
      console.log(`Test execution job ${job.id} completed:`, result);
    });

    testExecutionQueue.on("failed", (job, err) => {
      console.error(`Test execution job ${job.id} failed:`, err);
    });

    testExecutionQueue.on("progress", (job, progress) => {
      console.log(`Test execution job ${job.id} progress: ${progress}%`);
    });

    console.log("Redis and Bull queues setup completed");
  } catch (error) {
    console.error("Redis setup failed:", error);
    // Don't throw error to allow app to run without Redis in development
    if (process.env.NODE_ENV === "production") {
      throw error;
    }
  }
}

// Cache helper functions
async function setCache(key, value, expireInSeconds = 3600) {
  if (!redisClient) return false;
  try {
    await redisClient.setEx(key, expireInSeconds, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error("Cache set error:", error);
    return false;
  }
}

async function getCache(key) {
  if (!redisClient) return null;
  try {
    const value = await redisClient.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error("Cache get error:", error);
    return null;
  }
}

async function deleteCache(key) {
  if (!redisClient) return false;
  try {
    await redisClient.del(key);
    return true;
  } catch (error) {
    console.error("Cache delete error:", error);
    return false;
  }
}

async function clearCache(pattern = "*") {
  if (!redisClient) return false;
  try {
    const keys = await redisClient.keys(pattern);
    if (keys.length > 0) {
      await redisClient.del(keys);
    }
    return true;
  } catch (error) {
    console.error("Cache clear error:", error);
    return false;
  }
}

// Queue helper functions
async function addTestExecutionJob(data, options = {}) {
  if (!testExecutionQueue) return null;
  try {
    const job = await testExecutionQueue.add("execute-tests", data, {
      delay: options.delay || 0,
      attempts: options.attempts || 3,
      backoff: {
        type: "exponential",
        delay: 2000,
      },
      removeOnComplete: 10,
      removeOnFail: 5,
      ...options,
    });
    return job;
  } catch (error) {
    console.error("Failed to add test execution job:", error);
    return null;
  }
}

async function getJobStatus(jobId) {
  if (!testExecutionQueue) return null;
  try {
    const job = await testExecutionQueue.getJob(jobId);
    if (!job) return null;

    return {
      id: job.id,
      data: job.data,
      progress: job.progress(),
      state: await job.getState(),
      createdAt: job.timestamp,
      processedAt: job.processedOn,
      finishedAt: job.finishedOn,
      failedReason: job.failedReason,
    };
  } catch (error) {
    console.error("Failed to get job status:", error);
    return null;
  }
}

module.exports = {
  setupRedis,
  redisClient,
  testExecutionQueue,
  setCache,
  getCache,
  deleteCache,
  clearCache,
  addTestExecutionJob,
  getJobStatus,
};
