const jwt = require('jsonwebtoken');
const { db } = require('../config/database');

// Middleware to authenticate JWT tokens
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({ 
      error: 'Access token required',
      code: 'TOKEN_MISSING'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Fetch user from database to ensure they still exist and are active
    const user = await db('users')
      .where({ id: decoded.userId, is_active: true })
      .first();

    if (!user) {
      return res.status(401).json({ 
        error: 'Invalid token or user not found',
        code: 'INVALID_TOKEN'
      });
    }

    // Remove password hash from user object
    delete user.password_hash;
    
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ 
        error: 'Token expired',
        code: 'TOKEN_EXPIRED'
      });
    } else if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ 
        error: 'Invalid token',
        code: 'INVALID_TOKEN'
      });
    } else {
      console.error('Token verification error:', error);
      return res.status(500).json({ 
        error: 'Internal server error',
        code: 'SERVER_ERROR'
      });
    }
  }
};

// Middleware to check user roles
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const userRole = req.user.role;
    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    if (!allowedRoles.includes(userRole)) {
      return res.status(403).json({ 
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: allowedRoles,
        current: userRole
      });
    }

    next();
  };
};

// Middleware to check if user owns resource or has admin/manager role
const requireOwnershipOrRole = (resourceIdField = 'id', ownerField = 'created_by') => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ 
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const userId = req.user.id;
    const userRole = req.user.role;
    
    // Admin and manager roles have access to all resources
    if (['admin', 'manager'].includes(userRole)) {
      return next();
    }

    // For other roles, check ownership
    const resourceId = req.params[resourceIdField];
    
    if (!resourceId) {
      return res.status(400).json({ 
        error: 'Resource ID required',
        code: 'RESOURCE_ID_REQUIRED'
      });
    }

    try {
      // This is a generic check - specific routes should implement their own logic
      req.requireOwnershipCheck = {
        resourceId,
        ownerField,
        userId
      };
      next();
    } catch (error) {
      console.error('Ownership check error:', error);
      return res.status(500).json({ 
        error: 'Internal server error',
        code: 'SERVER_ERROR'
      });
    }
  };
};

// Middleware to validate API key for external integrations
const validateApiKey = async (req, res, next) => {
  const apiKey = req.headers['x-api-key'];

  if (!apiKey) {
    return res.status(401).json({ 
      error: 'API key required',
      code: 'API_KEY_MISSING'
    });
  }

  try {
    // Check if API key exists and is active
    const keyRecord = await db('api_keys')
      .where({ key: apiKey, is_active: true })
      .first();

    if (!keyRecord) {
      return res.status(401).json({ 
        error: 'Invalid API key',
        code: 'INVALID_API_KEY'
      });
    }

    // Update last used timestamp
    await db('api_keys')
      .where({ id: keyRecord.id })
      .update({ last_used_at: new Date() });

    req.apiKey = keyRecord;
    next();
  } catch (error) {
    console.error('API key validation error:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      code: 'SERVER_ERROR'
    });
  }
};

// Middleware to log API access
const logApiAccess = (req, res, next) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const logData = {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user ? req.user.id : null,
      timestamp: new Date().toISOString()
    };
    
    // Log to API access logger if available
    if (req.app.locals.logger && req.app.locals.logger.apiLogger) {
      req.app.locals.logger.apiLogger.info('API Access', logData);
    } else {
      console.log('API Access:', logData);
    }
  });
  
  next();
};

module.exports = {
  authenticateToken,
  requireRole,
  requireOwnershipOrRole,
  validateApiKey,
  logApiAccess
};
