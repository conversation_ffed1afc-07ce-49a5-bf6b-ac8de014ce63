const { setupLogger } = require('../config/logger');

const logger = setupLogger();

// Custom error class for application errors
class AppError extends Error {
  constructor(message, statusCode, code = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Middleware to handle 404 errors
const notFound = (req, res, next) => {
  const error = new AppError(`Not found - ${req.originalUrl}`, 404, 'NOT_FOUND');
  next(error);
};

// Global error handling middleware
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  logger.error('Error occurred:', {
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user ? req.user.id : null
  });

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found';
    error = new AppError(message, 404, 'RESOURCE_NOT_FOUND');
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = 'Duplicate field value entered';
    error = new AppError(message, 400, 'DUPLICATE_FIELD');
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message).join(', ');
    error = new AppError(message, 400, 'VALIDATION_ERROR');
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'Invalid token';
    error = new AppError(message, 401, 'INVALID_TOKEN');
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token expired';
    error = new AppError(message, 401, 'TOKEN_EXPIRED');
  }

  // SQLite errors
  if (err.code === 'SQLITE_CONSTRAINT_UNIQUE') {
    const message = 'Duplicate entry found';
    error = new AppError(message, 400, 'DUPLICATE_ENTRY');
  }

  if (err.code === 'SQLITE_CONSTRAINT_FOREIGNKEY') {
    const message = 'Referenced record not found';
    error = new AppError(message, 400, 'FOREIGN_KEY_CONSTRAINT');
  }

  // File upload errors
  if (err.code === 'LIMIT_FILE_SIZE') {
    const message = 'File too large';
    error = new AppError(message, 400, 'FILE_TOO_LARGE');
  }

  if (err.code === 'LIMIT_FILE_COUNT') {
    const message = 'Too many files';
    error = new AppError(message, 400, 'TOO_MANY_FILES');
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    const message = 'Unexpected file field';
    error = new AppError(message, 400, 'UNEXPECTED_FILE');
  }

  // Rate limiting errors
  if (err.status === 429) {
    const message = 'Too many requests, please try again later';
    error = new AppError(message, 429, 'RATE_LIMIT_EXCEEDED');
  }

  // Default to 500 server error
  const statusCode = error.statusCode || 500;
  const code = error.code || 'INTERNAL_SERVER_ERROR';

  // Send error response
  res.status(statusCode).json({
    success: false,
    error: {
      message: error.message || 'Internal Server Error',
      code: code,
      ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    }
  });
};

// Async error handler wrapper
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// Validation error formatter
const formatValidationErrors = (errors) => {
  return errors.array().map(error => ({
    field: error.param,
    message: error.msg,
    value: error.value
  }));
};

// Database error handler
const handleDatabaseError = (error) => {
  logger.error('Database error:', error);
  
  if (error.code === 'SQLITE_BUSY') {
    return new AppError('Database is busy, please try again', 503, 'DATABASE_BUSY');
  }
  
  if (error.code === 'SQLITE_LOCKED') {
    return new AppError('Database is locked, please try again', 503, 'DATABASE_LOCKED');
  }
  
  if (error.code === 'SQLITE_CORRUPT') {
    return new AppError('Database corruption detected', 500, 'DATABASE_CORRUPT');
  }
  
  return new AppError('Database operation failed', 500, 'DATABASE_ERROR');
};

// Test execution error handler
const handleTestExecutionError = (error, testId = null) => {
  logger.testLogger.error('Test execution error:', {
    error: error.message,
    stack: error.stack,
    testId
  });
  
  if (error.message.includes('timeout')) {
    return new AppError('Test execution timeout', 408, 'TEST_TIMEOUT');
  }
  
  if (error.message.includes('browser')) {
    return new AppError('Browser automation error', 500, 'BROWSER_ERROR');
  }
  
  return new AppError('Test execution failed', 500, 'TEST_EXECUTION_ERROR');
};

// File processing error handler
const handleFileProcessingError = (error, filename = null) => {
  logger.error('File processing error:', {
    error: error.message,
    filename
  });
  
  if (error.message.includes('ENOENT')) {
    return new AppError('File not found', 404, 'FILE_NOT_FOUND');
  }
  
  if (error.message.includes('EACCES')) {
    return new AppError('File access denied', 403, 'FILE_ACCESS_DENIED');
  }
  
  if (error.message.includes('parse')) {
    return new AppError('File parsing failed', 400, 'FILE_PARSE_ERROR');
  }
  
  return new AppError('File processing failed', 500, 'FILE_PROCESSING_ERROR');
};

module.exports = {
  AppError,
  notFound,
  errorHandler,
  asyncHandler,
  formatValidationErrors,
  handleDatabaseError,
  handleTestExecutionError,
  handleFileProcessingError
};
