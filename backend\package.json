{"name": "testing-tool-backend", "version": "1.0.0", "description": "Backend API for Testing Tool", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'No build step required for Node.js'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "node scripts/migrate.js", "db:seed": "node scripts/seed.js", "db:reset": "node scripts/reset.js"}, "dependencies": {"archiver": "^6.0.1", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "cors": "^2.8.5", "cypress": "^13.6.2", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "html-pdf": "^3.0.1", "jest": "^29.7.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lowdb": "^7.0.1", "mocha": "^10.2.0", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "papaparse": "^5.4.1", "pdf-lib": "^1.17.1", "playwright": "^1.40.1", "puppeteer": "^21.6.1", "redis": "^4.6.10", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^20.10.5", "eslint": "^8.56.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["testing", "api", "backend", "express"], "author": "Testing Tool Team", "license": "MIT"}