const express = require("express");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const { body, validationResult } = require("express-validator");
const { db } = require("../config/database");
const {
  asyncHandler,
  AppError,
  formatValidationErrors,
} = require("../middleware/errorHandler");
const { authenticateToken } = require("../middleware/auth");

const router = express.Router();

// Validation rules
const registerValidation = [
  body("username")
    .isLength({ min: 3, max: 30 })
    .withMessage("Username must be between 3 and 30 characters")
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage("Username can only contain letters, numbers, and underscores"),
  body("email")
    .isEmail()
    .withMessage("Please provide a valid email address")
    .normalizeEmail(),
  body("password")
    .isLength({ min: 6 })
    .withMessage("Password must be at least 6 characters long")
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage(
      "Password must contain at least one lowercase letter, one uppercase letter, and one number"
    ),
  body("firstName")
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage("First name must be between 1 and 50 characters"),
  body("lastName")
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage("Last name must be between 1 and 50 characters"),
];

const loginValidation = [
  body("username").notEmpty().withMessage("Username is required"),
  body("password").notEmpty().withMessage("Password is required"),
];

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || "24h",
  });
};

// @route   POST /api/auth/register
// @desc    Register a new user
// @access  Public
router.post(
  "/register",
  registerValidation,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: "Validation failed",
          code: "VALIDATION_ERROR",
          details: formatValidationErrors(errors),
        },
      });
    }

    const { username, email, password, firstName, lastName } = req.body;

    // Check if user already exists
    const existingUserByUsername = await db("users")
      .where({ username })
      .first();
    const existingUserByEmail = await db("users").where({ email }).first();

    if (existingUserByUsername || existingUserByEmail) {
      throw new AppError(
        "User already exists with this username or email",
        400,
        "USER_EXISTS"
      );
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user
    const [userId] = await db("users").insert({
      username,
      email,
      password_hash: passwordHash,
      first_name: firstName || null,
      last_name: lastName || null,
      role: "tester", // Default role
      is_active: true,
    });

    // Generate token
    const token = generateToken(userId);

    // Get user data (without password)
    const user = await db("users").where({ id: userId }).first();
    delete user.password_hash;

    res.status(201).json({
      success: true,
      data: {
        user,
        token,
      },
      message: "User registered successfully",
    });
  })
);

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post(
  "/login",
  loginValidation,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: "Validation failed",
          code: "VALIDATION_ERROR",
          details: formatValidationErrors(errors),
        },
      });
    }

    const { username, password } = req.body;

    // Find user by username or email
    console.log("Looking for user with username:", username);
    console.log("Database helper type:", typeof db);
    console.log("Database helper:", db);

    const usersTable = db("users");
    console.log("Users table:", usersTable);

    const whereClause = usersTable.where({ username });
    console.log("Where clause:", whereClause);

    let user = whereClause.first();
    console.log("User found by username:", user);
    if (!user) {
      user = db("users").where({ email: username }).first();
      console.log("User found by email:", user);
    }

    if (!user) {
      console.log("No user found");
      throw new AppError("Invalid credentials", 401, "INVALID_CREDENTIALS");
    }

    console.log("User object:", JSON.stringify(user, null, 2));
    console.log(
      "is_active value:",
      user.is_active,
      "type:",
      typeof user.is_active
    );
    if (!user.is_active) {
      throw new AppError("Account is deactivated", 401, "ACCOUNT_DEACTIVATED");
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password_hash);
    if (!isPasswordValid) {
      throw new AppError("Invalid credentials", 401, "INVALID_CREDENTIALS");
    }

    // Generate token
    const token = generateToken(user.id);

    // Remove password hash from response
    delete user.password_hash;

    res.json({
      success: true,
      data: {
        user,
        token,
      },
      message: "Login successful",
    });
  })
);

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Private
router.get(
  "/me",
  authenticateToken,
  asyncHandler(async (req, res) => {
    res.json({
      success: true,
      data: {
        user: req.user,
      },
    });
  })
);

// @route   PUT /api/auth/profile
// @desc    Update user profile
// @access  Private
router.put(
  "/profile",
  authenticateToken,
  [
    body("firstName")
      .optional()
      .isLength({ min: 1, max: 50 })
      .withMessage("First name must be between 1 and 50 characters"),
    body("lastName")
      .optional()
      .isLength({ min: 1, max: 50 })
      .withMessage("Last name must be between 1 and 50 characters"),
    body("email")
      .optional()
      .isEmail()
      .withMessage("Please provide a valid email address")
      .normalizeEmail(),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: "Validation failed",
          code: "VALIDATION_ERROR",
          details: formatValidationErrors(errors),
        },
      });
    }

    const { firstName, lastName, email } = req.body;
    const userId = req.user.id;

    // Check if email is already taken by another user
    if (email) {
      const existingUser = await db("users")
        .where("email", email)
        .where("id", "!=", userId)
        .first();

      if (existingUser) {
        throw new AppError("Email is already taken", 400, "EMAIL_TAKEN");
      }
    }

    // Update user
    await db("users").where("id", userId).update({
      first_name: firstName,
      last_name: lastName,
      email: email,
      updated_at: new Date(),
    });

    // Get updated user data
    const updatedUser = await db("users")
      .select(
        "id",
        "username",
        "email",
        "first_name",
        "last_name",
        "role",
        "is_active",
        "created_at",
        "updated_at"
      )
      .where("id", userId)
      .first();

    res.json({
      success: true,
      data: {
        user: updatedUser,
      },
      message: "Profile updated successfully",
    });
  })
);

// @route   PUT /api/auth/change-password
// @desc    Change user password
// @access  Private
router.put(
  "/change-password",
  authenticateToken,
  [
    body("currentPassword")
      .notEmpty()
      .withMessage("Current password is required"),
    body("newPassword")
      .isLength({ min: 6 })
      .withMessage("New password must be at least 6 characters long")
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .withMessage(
        "New password must contain at least one lowercase letter, one uppercase letter, and one number"
      ),
  ],
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          message: "Validation failed",
          code: "VALIDATION_ERROR",
          details: formatValidationErrors(errors),
        },
      });
    }

    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    // Get user with password hash
    const user = await db("users").where("id", userId).first();

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(
      currentPassword,
      user.password_hash
    );
    if (!isCurrentPasswordValid) {
      throw new AppError(
        "Current password is incorrect",
        400,
        "INVALID_CURRENT_PASSWORD"
      );
    }

    // Hash new password
    const saltRounds = 12;
    const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

    // Update password
    await db("users").where("id", userId).update({
      password_hash: newPasswordHash,
      updated_at: new Date(),
    });

    res.json({
      success: true,
      message: "Password changed successfully",
    });
  })
);

module.exports = router;
