const express = require('express');
const { asyncHandler, AppError } = require('../middleware/errorHandler');

const router = express.Router();

// @route   GET /api/reports
// @desc    Get all reports
// @access  Private
router.get('/', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      reports: [],
      message: 'Reporting dashboard coming soon!'
    }
  });
}));

// @route   POST /api/reports
// @desc    Generate a new report
// @access  Private
router.post('/', asyncHandler(async (req, res) => {
  res.status(501).json({
    success: false,
    error: {
      message: 'Reporting feature coming soon',
      code: 'FEATURE_NOT_IMPLEMENTED'
    }
  });
}));

module.exports = router;
