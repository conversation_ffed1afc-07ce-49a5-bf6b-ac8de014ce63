const express = require('express');
const { body, validationResult, query } = require('express-validator');
const { db } = require('../config/database');
const { asyncHandler, AppError, formatValidationErrors } = require('../middleware/errorHandler');
const { requireRole } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/test-cases
// @desc    Get all test cases with filtering and pagination
// @access  Private
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('suite_id').optional().isInt().withMessage('Suite ID must be an integer'),
  query('type').optional().isIn(['unit', 'integration', 'functional', 'api', 'ui', 'performance']),
  query('priority').optional().isIn(['low', 'medium', 'high', 'critical']),
  query('status').optional().isIn(['draft', 'active', 'deprecated']),
  query('search').optional().isLength({ min: 1 }).withMessage('Search term cannot be empty')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: formatValidationErrors(errors)
      }
    });
  }

  const {
    page = 1,
    limit = 25,
    suite_id,
    type,
    priority,
    status,
    search
  } = req.query;

  const offset = (page - 1) * limit;

  // Build query
  let query = db('test_cases')
    .select(
      'test_cases.*',
      'test_suites.name as suite_name',
      'users.username as created_by_username'
    )
    .leftJoin('test_suites', 'test_cases.suite_id', 'test_suites.id')
    .leftJoin('users', 'test_cases.created_by', 'users.id');

  // Apply filters
  if (suite_id) {
    query = query.where('test_cases.suite_id', suite_id);
  }
  if (type) {
    query = query.where('test_cases.type', type);
  }
  if (priority) {
    query = query.where('test_cases.priority', priority);
  }
  if (status) {
    query = query.where('test_cases.status', status);
  }
  if (search) {
    query = query.where(function() {
      this.where('test_cases.title', 'like', `%${search}%`)
          .orWhere('test_cases.description', 'like', `%${search}%`);
    });
  }

  // Get total count for pagination
  const totalQuery = query.clone().clearSelect().count('* as count').first();
  const total = await totalQuery;

  // Apply pagination and get results
  const testCases = await query
    .orderBy('test_cases.created_at', 'desc')
    .limit(limit)
    .offset(offset);

  res.json({
    success: true,
    data: {
      testCases,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: total.count,
        pages: Math.ceil(total.count / limit)
      }
    }
  });
}));

// @route   GET /api/test-cases/:id
// @desc    Get a single test case
// @access  Private
router.get('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  const testCase = await db('test_cases')
    .select(
      'test_cases.*',
      'test_suites.name as suite_name',
      'users.username as created_by_username'
    )
    .leftJoin('test_suites', 'test_cases.suite_id', 'test_suites.id')
    .leftJoin('users', 'test_cases.created_by', 'users.id')
    .where('test_cases.id', id)
    .first();

  if (!testCase) {
    throw new AppError('Test case not found', 404, 'TEST_CASE_NOT_FOUND');
  }

  res.json({
    success: true,
    data: {
      testCase
    }
  });
}));

// @route   POST /api/test-cases
// @desc    Create a new test case
// @access  Private (tester role or higher)
router.post('/', requireRole(['admin', 'manager', 'tester']), [
  body('title')
    .notEmpty()
    .withMessage('Title is required')
    .isLength({ max: 255 })
    .withMessage('Title must be less than 255 characters'),
  body('description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Description must be less than 2000 characters'),
  body('preconditions')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Preconditions must be less than 1000 characters'),
  body('test_steps')
    .optional()
    .isLength({ max: 5000 })
    .withMessage('Test steps must be less than 5000 characters'),
  body('expected_result')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Expected result must be less than 2000 characters'),
  body('type')
    .isIn(['unit', 'integration', 'functional', 'api', 'ui', 'performance'])
    .withMessage('Invalid test case type'),
  body('priority')
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid priority'),
  body('suite_id')
    .optional()
    .isInt()
    .withMessage('Suite ID must be an integer'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Tags must be an array'),
  body('custom_fields')
    .optional()
    .isObject()
    .withMessage('Custom fields must be an object')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: formatValidationErrors(errors)
      }
    });
  }

  const {
    title,
    description,
    preconditions,
    test_steps,
    expected_result,
    type,
    priority,
    suite_id,
    tags,
    custom_fields
  } = req.body;

  // Verify suite exists if provided
  if (suite_id) {
    const suite = await db('test_suites').where('id', suite_id).first();
    if (!suite) {
      throw new AppError('Test suite not found', 404, 'TEST_SUITE_NOT_FOUND');
    }
  }

  // Create test case
  const [testCaseId] = await db('test_cases').insert({
    title,
    description,
    preconditions,
    test_steps,
    expected_result,
    type,
    priority,
    suite_id,
    created_by: req.user.id,
    tags: tags ? JSON.stringify(tags) : null,
    custom_fields: custom_fields ? JSON.stringify(custom_fields) : null,
    status: 'draft'
  });

  // Get the created test case with related data
  const testCase = await db('test_cases')
    .select(
      'test_cases.*',
      'test_suites.name as suite_name',
      'users.username as created_by_username'
    )
    .leftJoin('test_suites', 'test_cases.suite_id', 'test_suites.id')
    .leftJoin('users', 'test_cases.created_by', 'users.id')
    .where('test_cases.id', testCaseId)
    .first();

  res.status(201).json({
    success: true,
    data: {
      testCase
    },
    message: 'Test case created successfully'
  });
}));

// @route   PUT /api/test-cases/:id
// @desc    Update a test case
// @access  Private (owner, admin, or manager)
router.put('/:id', [
  body('title')
    .optional()
    .notEmpty()
    .withMessage('Title cannot be empty')
    .isLength({ max: 255 })
    .withMessage('Title must be less than 255 characters'),
  body('description')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('Description must be less than 2000 characters'),
  body('type')
    .optional()
    .isIn(['unit', 'integration', 'functional', 'api', 'ui', 'performance'])
    .withMessage('Invalid test case type'),
  body('priority')
    .optional()
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid priority'),
  body('status')
    .optional()
    .isIn(['draft', 'active', 'deprecated'])
    .withMessage('Invalid status')
], asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: {
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: formatValidationErrors(errors)
      }
    });
  }

  const { id } = req.params;
  const updateData = req.body;

  // Check if test case exists
  const existingTestCase = await db('test_cases').where('id', id).first();
  if (!existingTestCase) {
    throw new AppError('Test case not found', 404, 'TEST_CASE_NOT_FOUND');
  }

  // Check ownership or role
  if (existingTestCase.created_by !== req.user.id && !['admin', 'manager'].includes(req.user.role)) {
    throw new AppError('Insufficient permissions', 403, 'INSUFFICIENT_PERMISSIONS');
  }

  // Update test case
  await db('test_cases')
    .where('id', id)
    .update({
      ...updateData,
      updated_at: new Date()
    });

  // Get updated test case
  const testCase = await db('test_cases')
    .select(
      'test_cases.*',
      'test_suites.name as suite_name',
      'users.username as created_by_username'
    )
    .leftJoin('test_suites', 'test_cases.suite_id', 'test_suites.id')
    .leftJoin('users', 'test_cases.created_by', 'users.id')
    .where('test_cases.id', id)
    .first();

  res.json({
    success: true,
    data: {
      testCase
    },
    message: 'Test case updated successfully'
  });
}));

// @route   DELETE /api/test-cases/:id
// @desc    Delete a test case
// @access  Private (owner, admin, or manager)
router.delete('/:id', asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Check if test case exists
  const testCase = await db('test_cases').where('id', id).first();
  if (!testCase) {
    throw new AppError('Test case not found', 404, 'TEST_CASE_NOT_FOUND');
  }

  // Check ownership or role
  if (testCase.created_by !== req.user.id && !['admin', 'manager'].includes(req.user.role)) {
    throw new AppError('Insufficient permissions', 403, 'INSUFFICIENT_PERMISSIONS');
  }

  // Delete test case
  await db('test_cases').where('id', id).del();

  res.json({
    success: true,
    message: 'Test case deleted successfully'
  });
}));

module.exports = router;
