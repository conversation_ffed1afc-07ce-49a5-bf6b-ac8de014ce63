const express = require('express');
const { asyncHandler, AppError } = require('../middleware/errorHandler');

const router = express.Router();

// @route   GET /api/test-data
// @desc    Get all test data sets
// @access  Private
router.get('/', asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      testDataSets: [],
      message: 'Test data management coming soon!'
    }
  });
}));

// @route   POST /api/test-data
// @desc    Create a new test data set
// @access  Private
router.post('/', asyncHandler(async (req, res) => {
  res.status(501).json({
    success: false,
    error: {
      message: 'Test data management feature coming soon',
      code: 'FEATURE_NOT_IMPLEMENTED'
    }
  });
}));

module.exports = router;
