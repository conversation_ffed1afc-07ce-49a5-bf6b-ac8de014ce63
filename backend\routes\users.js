const express = require('express');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requireRole } = require('../middleware/auth');

const router = express.Router();

// @route   GET /api/users
// @desc    Get all users (admin/manager only)
// @access  Private (admin/manager)
router.get('/', requireRole(['admin', 'manager']), asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      users: [],
      message: 'User management coming soon!'
    }
  });
}));

// @route   POST /api/users
// @desc    Create a new user (admin only)
// @access  Private (admin)
router.post('/', requireRole(['admin']), asyncHandler(async (req, res) => {
  res.status(501).json({
    success: false,
    error: {
      message: 'User management feature coming soon',
      code: 'FEATURE_NOT_IMPLEMENTED'
    }
  });
}));

module.exports = router;
