const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const rateLimit = require("express-rate-limit");
const { createServer } = require("http");
const { Server } = require("socket.io");
require("dotenv").config({ path: "../.env" });

const { initializeDatabase } = require("./config/database");
// const { setupRedis } = require("./config/redis");
const { setupLogger } = require("./config/logger");
const { errorHandler, notFound } = require("./middleware/errorHandler");
const { authenticateToken } = require("./middleware/auth");

// Import routes
const authRoutes = require("./routes/auth");
const testCaseRoutes = require("./routes/testCases");
const testDataRoutes = require("./routes/testData");
const testExecutionRoutes = require("./routes/testExecution");
const reportRoutes = require("./routes/reports");
const userRoutes = require("./routes/users");

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.FRONTEND_URL || "http://localhost:5173",
    methods: ["GET", "POST"],
  },
});

const PORT = process.env.PORT || 3001;
const logger = setupLogger();

// Rate limiting
const limiter = rateLimit({
  windowMs: (process.env.RATE_LIMIT_WINDOW || 15) * 60 * 1000, // 15 minutes
  max: process.env.RATE_LIMIT_MAX || 100, // limit each IP to 100 requests per windowMs
  message: "Too many requests from this IP, please try again later.",
});

// Middleware
app.use(helmet());
app.use(
  cors({
    origin: process.env.CORS_ORIGIN || "http://localhost:5173",
    credentials: true,
  })
);
app.use(
  morgan("combined", {
    stream: { write: (message) => logger.info(message.trim()) },
  })
);
app.use(limiter);
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Static file serving
app.use("/uploads", express.static("uploads"));
app.use("/reports", express.static("reports"));
app.use("/screenshots", express.static("screenshots"));
app.use("/videos", express.static("videos"));

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || "1.0.0",
  });
});

// Test database endpoint
app.get("/test-db", async (req, res) => {
  try {
    const fs = require("fs");
    const path = require("path");
    const dbPath = path.join(__dirname, "../data/testing-tool.json");
    const data = JSON.parse(fs.readFileSync(dbPath, "utf8"));
    const user = data.users.find((u) => u.username === "admin");
    res.json({
      success: true,
      user: user,
      totalUsers: data.users.length,
      message: "Database test successful",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});

// Test database path endpoint
app.get("/test-db-path", (req, res) => {
  try {
    const path = require("path");
    const fs = require("fs");

    const dbPath = path.join(__dirname, "../data/testing-tool.json");
    const absolutePath = path.resolve(dbPath);
    const exists = fs.existsSync(dbPath);

    let fileContent = null;
    let userData = null;

    if (exists) {
      fileContent = fs.readFileSync(dbPath, "utf8");
      const data = JSON.parse(fileContent);
      userData = data.users
        ? data.users.find((u) => u.username === "admin")
        : null;
    }

    res.json({
      success: true,
      dbPath: dbPath,
      absolutePath: absolutePath,
      exists: exists,
      hasUsers: fileContent ? JSON.parse(fileContent).users?.length : 0,
      adminUser: userData,
      message: "Database path test",
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack,
    });
  }
});

// Test database helper endpoint
app.get("/test-db-helper", (req, res) => {
  try {
    const { db } = require("./config/database");
    console.log("Testing database helper...");
    console.log("Database helper type:", typeof db);

    const usersTable = db("users");
    console.log("Users table type:", typeof usersTable);
    console.log("Users table methods:", Object.keys(usersTable));

    const whereClause = usersTable.where({ username: "admin" });
    console.log("Where clause type:", typeof whereClause);
    console.log("Where clause methods:", Object.keys(whereClause));

    console.log("About to call first()...");
    const user = whereClause.first();
    console.log("User result type:", typeof user);
    console.log("User result:", user);

    // If it's a promise, let's await it
    if (user && typeof user.then === "function") {
      console.log("User result is a promise, waiting...");
      user
        .then((result) => {
          console.log("Promise resolved to:", result);
          res.json({
            success: true,
            user: result,
            dbType: typeof db,
            usersTableType: typeof usersTable,
            whereClauseType: typeof whereClause,
            message: "Database helper test (promise resolved)",
          });
        })
        .catch((error) => {
          console.error("Promise rejected:", error);
          res.status(500).json({
            success: false,
            error: error.message,
          });
        });
    } else {
      res.json({
        success: true,
        user: user,
        dbType: typeof db,
        usersTableType: typeof usersTable,
        whereClauseType: typeof whereClause,
        message: "Database helper test (synchronous)",
      });
    }
  } catch (error) {
    console.error("Database helper test error:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack,
    });
  }
});

// API routes
app.use("/api/auth", authRoutes);
app.use("/api/test-cases", authenticateToken, testCaseRoutes);
app.use("/api/test-data", authenticateToken, testDataRoutes);
app.use("/api/test-execution", authenticateToken, testExecutionRoutes);
app.use("/api/reports", authenticateToken, reportRoutes);
app.use("/api/users", authenticateToken, userRoutes);

// WebSocket connection handling
io.on("connection", (socket) => {
  logger.info(`Client connected: ${socket.id}`);

  socket.on("join-room", (room) => {
    socket.join(room);
    logger.info(`Client ${socket.id} joined room: ${room}`);
  });

  socket.on("disconnect", () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
});

// Make io available to routes
app.set("io", io);

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Initialize services and start server
async function startServer() {
  try {
    // Initialize database
    await initializeDatabase();
    logger.info("Database initialized successfully");

    // Redis disabled for development
    logger.info("Redis disabled for development mode");

    // Start server
    server.listen(PORT, () => {
      logger.info(`Server running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV || "development"}`);
    });
  } catch (error) {
    logger.error("Failed to start server:", error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on("SIGTERM", () => {
  logger.info("SIGTERM received, shutting down gracefully");
  server.close(() => {
    logger.info("Process terminated");
  });
});

process.on("SIGINT", () => {
  logger.info("SIGINT received, shutting down gracefully");
  server.close(() => {
    logger.info("Process terminated");
  });
});

startServer();

module.exports = { app, server, io };
