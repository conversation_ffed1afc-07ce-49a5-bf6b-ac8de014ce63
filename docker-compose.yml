version: '3.8'

services:
  # Backend API Server
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DB_PATH=/app/data/testing-tool.db
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
      - ./reports:/app/reports
      - ./screenshots:/app/screenshots
      - ./videos:/app/videos
      - ./logs:/app/logs
    depends_on:
      - redis
    restart: unless-stopped

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "80:80"
    environment:
      - REACT_APP_API_URL=http://localhost:3001
    depends_on:
      - backend
    restart: unless-stopped

  # Redis for caching and queues
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Optional: Database backup service
  db-backup:
    image: alpine:latest
    volumes:
      - ./data:/data
      - ./backups:/backups
    command: >
      sh -c "
        while true; do
          cp /data/testing-tool.db /backups/testing-tool-$(date +%Y%m%d_%H%M%S).db
          find /backups -name '*.db' -mtime +7 -delete
          sleep 86400
        done
      "
    restart: unless-stopped

volumes:
  redis_data:

networks:
  default:
    name: testing-tool-network
