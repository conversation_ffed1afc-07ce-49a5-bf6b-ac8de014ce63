{"name": "testing-tool-frontend", "version": "1.0.0", "description": "Frontend for Testing Tool", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx,ts,tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-query": "^3.39.3", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.18.2", "@mui/x-date-pickers": "^6.18.2", "recharts": "^2.8.0", "xlsx": "^0.18.5", "papaparse": "^5.4.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-hot-toast": "^2.4.1", "date-fns": "^2.30.0", "lodash": "^4.17.21", "react-beautiful-dnd": "^13.1.1", "react-syntax-highlighter": "^15.5.0", "monaco-editor": "^0.45.0", "@monaco-editor/react": "^4.6.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "file-saver": "^2.0.5"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}, "keywords": ["testing", "frontend", "react", "vite"], "author": "Testing Tool Team", "license": "MIT"}