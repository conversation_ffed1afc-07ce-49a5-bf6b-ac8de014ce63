import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Box } from '@mui/material'

import { useAuth } from './context/AuthContext'
import Layout from './components/Layout/Layout'
import LoadingSpinner from './components/Common/LoadingSpinner'

// Pages
import LoginPage from './pages/Auth/LoginPage'
import RegisterPage from './pages/Auth/RegisterPage'
import DashboardPage from './pages/Dashboard/DashboardPage'
import TestCasesPage from './pages/TestCases/TestCasesPage'
import TestSuitesPage from './pages/TestSuites/TestSuitesPage'
import TestExecutionPage from './pages/TestExecution/TestExecutionPage'
import ReportsPage from './pages/Reports/ReportsPage'
import TestDataPage from './pages/TestData/TestDataPage'
import SettingsPage from './pages/Settings/SettingsPage'
import ProfilePage from './pages/Profile/ProfilePage'
import NotFoundPage from './pages/NotFound/NotFoundPage'

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        minHeight="100vh"
      >
        <LoadingSpinner size={60} />
      </Box>
    )
  }

  if (!user) {
    return <Navigate to="/login" replace />
  }

  return children
}

// Public Route Component (redirect to dashboard if already logged in)
const PublicRoute = ({ children }) => {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        minHeight="100vh"
      >
        <LoadingSpinner size={60} />
      </Box>
    )
  }

  if (user) {
    return <Navigate to="/dashboard" replace />
  }

  return children
}

function App() {
  return (
    <Box className="App" sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <Routes>
        {/* Public Routes */}
        <Route 
          path="/login" 
          element={
            <PublicRoute>
              <LoginPage />
            </PublicRoute>
          } 
        />
        <Route 
          path="/register" 
          element={
            <PublicRoute>
              <RegisterPage />
            </PublicRoute>
          } 
        />

        {/* Protected Routes */}
        <Route 
          path="/*" 
          element={
            <ProtectedRoute>
              <Layout>
                <Routes>
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />
                  <Route path="/dashboard" element={<DashboardPage />} />
                  <Route path="/test-cases/*" element={<TestCasesPage />} />
                  <Route path="/test-suites/*" element={<TestSuitesPage />} />
                  <Route path="/test-execution/*" element={<TestExecutionPage />} />
                  <Route path="/test-data/*" element={<TestDataPage />} />
                  <Route path="/reports/*" element={<ReportsPage />} />
                  <Route path="/profile" element={<ProfilePage />} />
                  <Route path="/settings/*" element={<SettingsPage />} />
                  <Route path="*" element={<NotFoundPage />} />
                </Routes>
              </Layout>
            </ProtectedRoute>
          } 
        />
      </Routes>
    </Box>
  )
}

export default App
