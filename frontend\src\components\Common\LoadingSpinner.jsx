import React from 'react'
import { Box, CircularProgress, Typography } from '@mui/material'

const LoadingSpinner = ({ 
  size = 40, 
  message = 'Loading...', 
  showMessage = true,
  color = 'primary',
  variant = 'indeterminate',
  value,
  thickness = 3.6
}) => {
  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      gap={2}
      p={2}
    >
      <CircularProgress
        size={size}
        color={color}
        variant={variant}
        value={value}
        thickness={thickness}
      />
      {showMessage && (
        <Typography
          variant="body2"
          color="text.secondary"
          textAlign="center"
        >
          {message}
        </Typography>
      )}
    </Box>
  )
}

export default LoadingSpinner
