import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { useQuery, useMutation, useQueryClient } from 'react-query'
import toast from 'react-hot-toast'

import { authService } from '../services/authService'
import { CACHE } from '@constants'

// Auth Context
const AuthContext = createContext()

// Auth Actions
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_TOKEN: 'SET_TOKEN',
  LOGOUT: 'LOGOUT',
  SET_ERROR: 'SET_ERROR'
}

// Initial State
const initialState = {
  user: null,
  token: localStorage.getItem('token'),
  loading: true,
  error: null
}

// Auth Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      }
    case AUTH_ACTIONS.SET_USER:
      return {
        ...state,
        user: action.payload,
        loading: false,
        error: null
      }
    case AUTH_ACTIONS.SET_TOKEN:
      return {
        ...state,
        token: action.payload
      }
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        token: null,
        loading: false,
        error: null
      }
    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false
      }
    default:
      return state
  }
}

// Auth Provider Component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState)
  const queryClient = useQueryClient()

  // Get current user query
  const { data: userData, isLoading: userLoading, error: userError } = useQuery(
    [CACHE.KEYS.USER_PROFILE],
    authService.getCurrentUser,
    {
      enabled: !!state.token,
      retry: false,
      staleTime: CACHE.DEFAULT_TTL,
      onSuccess: (data) => {
        dispatch({ type: AUTH_ACTIONS.SET_USER, payload: data.user })
      },
      onError: (error) => {
        console.error('Failed to get current user:', error)
        if (error.response?.status === 401) {
          logout()
        } else {
          dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: error.message })
        }
      }
    }
  )

  // Login mutation
  const loginMutation = useMutation(authService.login, {
    onSuccess: (data) => {
      const { user, token } = data
      
      // Store token in localStorage
      localStorage.setItem('token', token)
      
      // Update state
      dispatch({ type: AUTH_ACTIONS.SET_TOKEN, payload: token })
      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: user })
      
      // Update query cache
      queryClient.setQueryData([CACHE.KEYS.USER_PROFILE], data)
      
      toast.success('Login successful!')
    },
    onError: (error) => {
      const message = error.response?.data?.error?.message || 'Login failed'
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: message })
      toast.error(message)
    }
  })

  // Register mutation
  const registerMutation = useMutation(authService.register, {
    onSuccess: (data) => {
      const { user, token } = data
      
      // Store token in localStorage
      localStorage.setItem('token', token)
      
      // Update state
      dispatch({ type: AUTH_ACTIONS.SET_TOKEN, payload: token })
      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: user })
      
      // Update query cache
      queryClient.setQueryData([CACHE.KEYS.USER_PROFILE], data)
      
      toast.success('Registration successful!')
    },
    onError: (error) => {
      const message = error.response?.data?.error?.message || 'Registration failed'
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: message })
      toast.error(message)
    }
  })

  // Update profile mutation
  const updateProfileMutation = useMutation(authService.updateProfile, {
    onSuccess: (data) => {
      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: data.user })
      queryClient.setQueryData([CACHE.KEYS.USER_PROFILE], data)
      toast.success('Profile updated successfully!')
    },
    onError: (error) => {
      const message = error.response?.data?.error?.message || 'Profile update failed'
      toast.error(message)
    }
  })

  // Change password mutation
  const changePasswordMutation = useMutation(authService.changePassword, {
    onSuccess: () => {
      toast.success('Password changed successfully!')
    },
    onError: (error) => {
      const message = error.response?.data?.error?.message || 'Password change failed'
      toast.error(message)
    }
  })

  // Login function
  const login = async (credentials) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true })
    try {
      await loginMutation.mutateAsync(credentials)
    } catch (error) {
      // Error handling is done in the mutation
    } finally {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false })
    }
  }

  // Register function
  const register = async (userData) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true })
    try {
      await registerMutation.mutateAsync(userData)
    } catch (error) {
      // Error handling is done in the mutation
    } finally {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false })
    }
  }

  // Logout function
  const logout = () => {
    // Remove token from localStorage
    localStorage.removeItem('token')
    
    // Clear state
    dispatch({ type: AUTH_ACTIONS.LOGOUT })
    
    // Clear all query cache
    queryClient.clear()
    
    toast.success('Logged out successfully!')
  }

  // Update profile function
  const updateProfile = async (profileData) => {
    try {
      await updateProfileMutation.mutateAsync(profileData)
    } catch (error) {
      // Error handling is done in the mutation
    }
  }

  // Change password function
  const changePassword = async (passwordData) => {
    try {
      await changePasswordMutation.mutateAsync(passwordData)
    } catch (error) {
      // Error handling is done in the mutation
    }
  }

  // Check if user has permission
  const hasPermission = (permission) => {
    if (!state.user) return false
    
    // Admin has all permissions
    if (state.user.role === 'admin') return true
    
    // Check role-based permissions (implement based on your permission system)
    // This is a simplified version
    const rolePermissions = {
      manager: ['read', 'write', 'execute'],
      tester: ['read', 'write', 'execute'],
      viewer: ['read']
    }
    
    return rolePermissions[state.user.role]?.includes(permission) || false
  }

  // Check if user has role
  const hasRole = (role) => {
    return state.user?.role === role
  }

  // Initialize auth state
  useEffect(() => {
    if (!state.token) {
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false })
    }
  }, [state.token])

  // Context value
  const value = {
    user: state.user,
    token: state.token,
    loading: state.loading || userLoading,
    error: state.error || userError,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    hasPermission,
    hasRole,
    isLoading: loginMutation.isLoading || registerMutation.isLoading || updateProfileMutation.isLoading || changePasswordMutation.isLoading
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
