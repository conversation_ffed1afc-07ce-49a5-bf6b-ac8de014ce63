import React, { createContext, useContext, useEffect, useState } from 'react'
import { io } from 'socket.io-client'
import toast from 'react-hot-toast'

import { useAuth } from './AuthContext'
import { API_CONFIG, WEBSOCKET, SocketEvents } from '@constants'

// Socket Context
const SocketContext = createContext()

// Socket Provider Component
export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null)
  const [connected, setConnected] = useState(false)
  const [reconnectAttempts, setReconnectAttempts] = useState(0)
  const { user, token } = useAuth()

  // Initialize socket connection
  useEffect(() => {
    if (user && token) {
      const newSocket = io(API_CONFIG.BASE_URL, {
        auth: {
          token
        },
        timeout: WEBSOCKET.CONNECTION_TIMEOUT,
        reconnection: true,
        reconnectionAttempts: WEBSOCKET.RECONNECT_ATTEMPTS,
        reconnectionDelay: WEBSOCKET.RECONNECT_DELAY
      })

      // Connection event handlers
      newSocket.on('connect', () => {
        console.log('Socket connected:', newSocket.id)
        setConnected(true)
        setReconnectAttempts(0)
        
        // Join user-specific room
        newSocket.emit('join-room', `user-${user.id}`)
      })

      newSocket.on('disconnect', (reason) => {
        console.log('Socket disconnected:', reason)
        setConnected(false)
      })

      newSocket.on('connect_error', (error) => {
        console.error('Socket connection error:', error)
        setConnected(false)
      })

      newSocket.on('reconnect', (attemptNumber) => {
        console.log('Socket reconnected after', attemptNumber, 'attempts')
        setConnected(true)
        setReconnectAttempts(0)
        toast.success('Connection restored')
      })

      newSocket.on('reconnect_attempt', (attemptNumber) => {
        console.log('Socket reconnection attempt:', attemptNumber)
        setReconnectAttempts(attemptNumber)
      })

      newSocket.on('reconnect_failed', () => {
        console.error('Socket reconnection failed')
        toast.error('Connection lost. Please refresh the page.')
      })

      // Test execution event handlers
      newSocket.on(SocketEvents.TEST_EXECUTION_STARTED, (data) => {
        toast.success(`Test execution started: ${data.name}`)
      })

      newSocket.on(SocketEvents.TEST_EXECUTION_PROGRESS, (data) => {
        // Handle progress updates (could update a progress bar)
        console.log('Test execution progress:', data)
      })

      newSocket.on(SocketEvents.TEST_EXECUTION_COMPLETED, (data) => {
        toast.success(`Test execution completed: ${data.name}`)
      })

      newSocket.on(SocketEvents.TEST_EXECUTION_FAILED, (data) => {
        toast.error(`Test execution failed: ${data.name}`)
      })

      // System notification handlers
      newSocket.on(SocketEvents.SYSTEM_NOTIFICATION, (data) => {
        toast(data.message, {
          icon: data.type === 'info' ? 'ℹ️' : '📢'
        })
      })

      newSocket.on(SocketEvents.ERROR_NOTIFICATION, (data) => {
        toast.error(data.message)
      })

      setSocket(newSocket)

      // Cleanup on unmount
      return () => {
        newSocket.close()
      }
    } else {
      // Disconnect socket if user is not authenticated
      if (socket) {
        socket.close()
        setSocket(null)
        setConnected(false)
      }
    }
  }, [user, token])

  // Heartbeat to keep connection alive
  useEffect(() => {
    if (socket && connected) {
      const heartbeat = setInterval(() => {
        socket.emit('heartbeat')
      }, WEBSOCKET.HEARTBEAT_INTERVAL)

      return () => clearInterval(heartbeat)
    }
  }, [socket, connected])

  // Socket utility functions
  const emit = (event, data) => {
    if (socket && connected) {
      socket.emit(event, data)
    } else {
      console.warn('Socket not connected. Cannot emit event:', event)
    }
  }

  const on = (event, callback) => {
    if (socket) {
      socket.on(event, callback)
    }
  }

  const off = (event, callback) => {
    if (socket) {
      socket.off(event, callback)
    }
  }

  const joinRoom = (room) => {
    emit('join-room', room)
  }

  const leaveRoom = (room) => {
    emit('leave-room', room)
  }

  // Subscribe to test execution updates
  const subscribeToTestExecution = (executionId, callbacks = {}) => {
    const events = [
      { event: SocketEvents.TEST_EXECUTION_STARTED, callback: callbacks.onStarted },
      { event: SocketEvents.TEST_EXECUTION_PROGRESS, callback: callbacks.onProgress },
      { event: SocketEvents.TEST_EXECUTION_COMPLETED, callback: callbacks.onCompleted },
      { event: SocketEvents.TEST_EXECUTION_FAILED, callback: callbacks.onFailed }
    ]

    events.forEach(({ event, callback }) => {
      if (callback) {
        const wrappedCallback = (data) => {
          if (data.executionId === executionId) {
            callback(data)
          }
        }
        on(event, wrappedCallback)
      }
    })

    // Join execution-specific room
    joinRoom(`execution-${executionId}`)

    // Return unsubscribe function
    return () => {
      events.forEach(({ event, callback }) => {
        if (callback) {
          off(event, callback)
        }
      })
      leaveRoom(`execution-${executionId}`)
    }
  }

  // Subscribe to project updates
  const subscribeToProject = (projectId, callbacks = {}) => {
    const events = [
      { event: SocketEvents.TEST_CASE_CREATED, callback: callbacks.onTestCaseCreated },
      { event: SocketEvents.TEST_CASE_UPDATED, callback: callbacks.onTestCaseUpdated },
      { event: SocketEvents.TEST_CASE_DELETED, callback: callbacks.onTestCaseDeleted }
    ]

    events.forEach(({ event, callback }) => {
      if (callback) {
        const wrappedCallback = (data) => {
          if (data.projectId === projectId) {
            callback(data)
          }
        }
        on(event, wrappedCallback)
      }
    })

    // Join project-specific room
    joinRoom(`project-${projectId}`)

    // Return unsubscribe function
    return () => {
      events.forEach(({ event, callback }) => {
        if (callback) {
          off(event, callback)
        }
      })
      leaveRoom(`project-${projectId}`)
    }
  }

  const value = {
    socket,
    connected,
    reconnectAttempts,
    emit,
    on,
    off,
    joinRoom,
    leaveRoom,
    subscribeToTestExecution,
    subscribeToProject
  }

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  )
}

// Custom hook to use socket context
export const useSocket = () => {
  const context = useContext(SocketContext)
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider')
  }
  return context
}
