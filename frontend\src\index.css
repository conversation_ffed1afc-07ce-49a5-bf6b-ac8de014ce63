/* Global styles */
* {
  box-sizing: border-box;
}

html {
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto', 'Helvetica', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  background-color: #f5f5f5;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Loading spinner */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

.mt-1 {
  margin-top: 8px;
}

.mt-2 {
  margin-top: 16px;
}

.mt-3 {
  margin-top: 24px;
}

.mb-1 {
  margin-bottom: 8px;
}

.mb-2 {
  margin-bottom: 16px;
}

.mb-3 {
  margin-bottom: 24px;
}

.ml-1 {
  margin-left: 8px;
}

.ml-2 {
  margin-left: 16px;
}

.mr-1 {
  margin-right: 8px;
}

.mr-2 {
  margin-right: 16px;
}

.p-1 {
  padding: 8px;
}

.p-2 {
  padding: 16px;
}

.p-3 {
  padding: 24px;
}

/* Custom animations */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Test status colors */
.status-pass {
  color: #2e7d32;
  background-color: #e8f5e8;
}

.status-fail {
  color: #d32f2f;
  background-color: #ffeaea;
}

.status-skip {
  color: #ed6c02;
  background-color: #fff4e6;
}

.status-error {
  color: #9c27b0;
  background-color: #f3e5f5;
}

.status-pending {
  color: #1976d2;
  background-color: #e3f2fd;
}

.status-running {
  color: #689f38;
  background-color: #f1f8e9;
}

/* Priority colors */
.priority-low {
  color: #689f38;
}

.priority-medium {
  color: #ed6c02;
}

.priority-high {
  color: #d32f2f;
}

.priority-critical {
  color: #9c27b0;
}

/* Drag and drop styles */
.drag-over {
  border: 2px dashed #1976d2;
  background-color: rgba(25, 118, 210, 0.1);
}

.dragging {
  opacity: 0.5;
}

/* Code editor styles */
.monaco-editor {
  border-radius: 8px;
}

/* Data grid custom styles */
.MuiDataGrid-root {
  border: none;
  border-radius: 8px;
}

.MuiDataGrid-cell:focus {
  outline: none;
}

.MuiDataGrid-row:hover {
  background-color: rgba(25, 118, 210, 0.04);
}

/* Chart container */
.chart-container {
  width: 100%;
  height: 400px;
}

/* File upload area */
.file-upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-upload-area:hover {
  border-color: #1976d2;
  background-color: rgba(25, 118, 210, 0.04);
}

.file-upload-area.active {
  border-color: #1976d2;
  background-color: rgba(25, 118, 210, 0.08);
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
}
