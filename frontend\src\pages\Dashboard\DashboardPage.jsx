import React from 'react'
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Button
} from '@mui/material'
import {
  Assignment,
  PlayArrow,
  CheckCircle,
  Error,
  Warning,
  Schedule,
  TrendingUp,
  People
} from '@mui/icons-material'

import { useAuth } from '../../context/AuthContext'

const DashboardPage = () => {
  const { user } = useAuth()

  // Mock data - replace with real data from API
  const stats = {
    totalTestCases: 156,
    passedTests: 142,
    failedTests: 8,
    skippedTests: 6,
    executionsToday: 12,
    activeProjects: 5
  }

  const recentExecutions = [
    { id: 1, name: 'Login Flow Tests', status: 'passed', time: '2 hours ago' },
    { id: 2, name: 'API Integration Tests', status: 'failed', time: '4 hours ago' },
    { id: 3, name: 'UI Regression Tests', status: 'passed', time: '6 hours ago' },
    { id: 4, name: 'Performance Tests', status: 'running', time: 'Running now' }
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case 'passed': return 'success'
      case 'failed': return 'error'
      case 'running': return 'info'
      case 'skipped': return 'warning'
      default: return 'default'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'passed': return <CheckCircle />
      case 'failed': return <Error />
      case 'running': return <Schedule />
      case 'skipped': return <Warning />
      default: return <Assignment />
    }
  }

  return (
    <Box>
      {/* Welcome Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Welcome back, {user?.first_name || user?.username}!
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Here's an overview of your testing activities
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Assignment sx={{ color: 'primary.main', mr: 1 }} />
                <Typography variant="h6">Test Cases</Typography>
              </Box>
              <Typography variant="h4" color="primary">
                {stats.totalTestCases}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total test cases
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CheckCircle sx={{ color: 'success.main', mr: 1 }} />
                <Typography variant="h6">Passed</Typography>
              </Box>
              <Typography variant="h4" color="success.main">
                {stats.passedTests}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tests passed
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Error sx={{ color: 'error.main', mr: 1 }} />
                <Typography variant="h6">Failed</Typography>
              </Box>
              <Typography variant="h4" color="error.main">
                {stats.failedTests}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tests failed
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PlayArrow sx={{ color: 'info.main', mr: 1 }} />
                <Typography variant="h6">Executions</Typography>
              </Box>
              <Typography variant="h4" color="info.main">
                {stats.executionsToday}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Today's executions
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Test Executions */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">Recent Test Executions</Typography>
              <Button variant="outlined" size="small">
                View All
              </Button>
            </Box>
            <List>
              {recentExecutions.map((execution) => (
                <ListItem key={execution.id} divider>
                  <ListItemIcon>
                    {getStatusIcon(execution.status)}
                  </ListItemIcon>
                  <ListItemText
                    primary={execution.name}
                    secondary={execution.time}
                  />
                  <Chip
                    label={execution.status}
                    color={getStatusColor(execution.status)}
                    size="small"
                    variant="outlined"
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Quick Actions
            </Typography>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Button
                variant="contained"
                startIcon={<Assignment />}
                fullWidth
              >
                Create Test Case
              </Button>
              <Button
                variant="contained"
                startIcon={<PlayArrow />}
                fullWidth
                color="success"
              >
                Run Tests
              </Button>
              <Button
                variant="outlined"
                startIcon={<TrendingUp />}
                fullWidth
              >
                View Reports
              </Button>
              <Button
                variant="outlined"
                startIcon={<People />}
                fullWidth
              >
                Manage Team
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  )
}

export default DashboardPage
