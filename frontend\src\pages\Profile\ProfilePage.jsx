import React from 'react'
import {
  Box,
  Typography,
  Paper,
  Chip
} from '@mui/material'
import { AccountCircle } from '@mui/icons-material'

const ProfilePage = () => {
  return (
    <Box>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Profile
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your account settings and preferences
        </Typography>
      </Box>

      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <AccountCircle sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          User Profile Management
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          This feature is coming soon! You'll be able to manage your profile here.
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Chip label="Personal Information" variant="outlined" />
          <Chip label="Change Password" variant="outlined" />
          <Chip label="Preferences" variant="outlined" />
          <Chip label="Activity History" variant="outlined" />
        </Box>
      </Paper>
    </Box>
  )
}

export default ProfilePage
