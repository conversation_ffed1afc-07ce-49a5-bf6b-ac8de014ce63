import React from 'react'
import {
  Box,
  Typography,
  Button,
  Paper,
  Chip
} from '@mui/material'
import { Add, Assessment } from '@mui/icons-material'

const ReportsPage = () => {
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Reports & Analytics
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Generate comprehensive test reports and analytics
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          size="large"
        >
          Generate Report
        </Button>
      </Box>

      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Assessment sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          Reporting & Analytics Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          This feature is coming soon! You'll be able to generate detailed reports here.
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Chip label="Visual Charts" variant="outlined" />
          <Chip label="Trend Analysis" variant="outlined" />
          <Chip label="PDF Export" variant="outlined" />
          <Chip label="Custom Reports" variant="outlined" />
        </Box>
      </Paper>
    </Box>
  )
}

export default ReportsPage
