import React from 'react'
import {
  Box,
  Typography,
  Paper,
  Chip
} from '@mui/material'
import { Settings } from '@mui/icons-material'

const SettingsPage = () => {
  return (
    <Box>
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Settings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Configure your testing environment and preferences
        </Typography>
      </Box>

      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Settings sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          System Configuration
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          This feature is coming soon! You'll be able to configure system settings here.
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Chip label="User Management" variant="outlined" />
          <Chip label="Project Settings" variant="outlined" />
          <Chip label="Integrations" variant="outlined" />
          <Chip label="Notifications" variant="outlined" />
        </Box>
      </Paper>
    </Box>
  )
}

export default SettingsPage
