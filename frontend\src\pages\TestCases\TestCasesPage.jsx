import React from 'react'
import {
  Box,
  Typography,
  Button,
  Paper,
  Chip
} from '@mui/material'
import { Add, Assignment } from '@mui/icons-material'

const TestCasesPage = () => {
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Test Cases
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage and organize your test cases
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          size="large"
        >
          Create Test Case
        </Button>
      </Box>

      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Assignment sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          Test Case Management
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          This feature is coming soon! You'll be able to create, edit, and manage test cases here.
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Chip label="Create Test Cases" variant="outlined" />
          <Chip label="Organize Test Suites" variant="outlined" />
          <Chip label="Import/Export" variant="outlined" />
          <Chip label="Templates" variant="outlined" />
        </Box>
      </Paper>
    </Box>
  )
}

export default TestCasesPage
