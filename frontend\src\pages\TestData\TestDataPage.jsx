import React from 'react'
import {
  Box,
  Typography,
  Button,
  Paper,
  Chip
} from '@mui/material'
import { Upload, Storage } from '@mui/icons-material'

const TestDataPage = () => {
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Test Data
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage test data sets and files
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Upload />}
          size="large"
        >
          Upload Data
        </Button>
      </Box>

      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <Storage sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          Test Data Management
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          This feature is coming soon! You'll be able to manage test data files here.
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Chip label="Excel Import/Export" variant="outlined" />
          <Chip label="CSV Processing" variant="outlined" />
          <Chip label="Data Validation" variant="outlined" />
          <Chip label="Mock Data Generation" variant="outlined" />
        </Box>
      </Paper>
    </Box>
  )
}

export default TestDataPage
