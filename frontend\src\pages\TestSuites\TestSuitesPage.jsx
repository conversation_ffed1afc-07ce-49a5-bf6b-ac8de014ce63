import React from 'react'
import {
  Box,
  Typography,
  Button,
  Paper,
  Chip
} from '@mui/material'
import { Add, FolderOpen } from '@mui/icons-material'

const TestSuitesPage = () => {
  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Test Suites
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Organize test cases into logical groups
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Add />}
          size="large"
        >
          Create Test Suite
        </Button>
      </Box>

      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <FolderOpen sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          Test Suite Management
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          This feature is coming soon! You'll be able to create and manage test suites here.
        </Typography>
        <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center', flexWrap: 'wrap' }}>
          <Chip label="Group Test Cases" variant="outlined" />
          <Chip label="Suite Configuration" variant="outlined" />
          <Chip label="Execution Order" variant="outlined" />
          <Chip label="Dependencies" variant="outlined" />
        </Box>
      </Paper>
    </Box>
  )
}

export default TestSuitesPage
