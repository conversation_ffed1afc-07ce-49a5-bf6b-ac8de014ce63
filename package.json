{"name": "testing-tool", "version": "1.0.0", "description": "Comprehensive Test Management Platform", "main": "backend/server.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "cd backend && npm start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "keywords": ["testing", "test-management", "automation", "reporting", "excel", "csv"], "author": "Testing Tool Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["backend", "frontend", "shared"]}