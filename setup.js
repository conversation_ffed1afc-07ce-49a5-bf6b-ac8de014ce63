#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Testing Tool...\n');

// Check Node.js version
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 18) {
  console.error('❌ Node.js 18 or higher is required. Current version:', nodeVersion);
  process.exit(1);
}

console.log('✅ Node.js version check passed:', nodeVersion);

// Function to run command and handle errors
function runCommand(command, cwd = process.cwd()) {
  try {
    console.log(`📦 Running: ${command}`);
    execSync(command, { 
      cwd, 
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'development' }
    });
    return true;
  } catch (error) {
    console.error(`❌ Failed to run: ${command}`);
    console.error(error.message);
    return false;
  }
}

// Function to check if file exists
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

// Function to copy file if it doesn't exist
function copyFileIfNotExists(src, dest) {
  if (!fileExists(dest)) {
    try {
      fs.copyFileSync(src, dest);
      console.log(`✅ Created ${dest}`);
      return true;
    } catch (error) {
      console.error(`❌ Failed to create ${dest}:`, error.message);
      return false;
    }
  } else {
    console.log(`ℹ️  ${dest} already exists, skipping...`);
    return true;
  }
}

async function setup() {
  try {
    // 1. Install root dependencies
    console.log('\n📦 Installing root dependencies...');
    if (!runCommand('pnpm install')) {
      throw new Error('Failed to install root dependencies');
    }

    // 2. Install backend dependencies
    console.log('\n📦 Installing backend dependencies...');
    if (!runCommand('pnpm install', './backend')) {
      throw new Error('Failed to install backend dependencies');
    }

    // 3. Install frontend dependencies
    console.log('\n📦 Installing frontend dependencies...');
    if (!runCommand('pnpm install', './frontend')) {
      throw new Error('Failed to install frontend dependencies');
    }

    // 4. Create .env file if it doesn't exist
    console.log('\n⚙️  Setting up environment variables...');
    if (!copyFileIfNotExists('.env.example', '.env')) {
      throw new Error('Failed to create .env file');
    }

    // 5. Create necessary directories
    console.log('\n📁 Creating necessary directories...');
    const directories = [
      'data',
      'uploads',
      'reports', 
      'screenshots',
      'videos',
      'logs',
      'backups'
    ];

    directories.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`✅ Created directory: ${dir}`);
      } else {
        console.log(`ℹ️  Directory ${dir} already exists`);
      }
    });

    // 6. Initialize database
    console.log('\n🗄️  Initializing database...');
    if (!runCommand('pnpm run db:migrate', './backend')) {
      throw new Error('Failed to initialize database');
    }

    // 7. Seed database with sample data
    console.log('\n🌱 Seeding database with sample data...');
    if (!runCommand('pnpm run db:seed', './backend')) {
      throw new Error('Failed to seed database');
    }

    // 8. Success message
    console.log('\n🎉 Setup completed successfully!\n');
    console.log('📋 Next steps:');
    console.log('1. Review and update the .env file with your configuration');
    console.log('2. Start the development servers: pnpm run dev');
    console.log('3. Open your browser to: http://localhost:5173');
    console.log('\n🔑 Default login credentials:');
    console.log('   Username: admin');
    console.log('   Password: admin123');
    console.log('\n📚 For more information, see the README.md file');

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure you have Node.js 18+ installed');
    console.log('2. Check your internet connection');
    console.log('3. Try running the setup again');
    console.log('4. Check the logs above for specific error details');
    process.exit(1);
  }
}

// Run setup
setup();
