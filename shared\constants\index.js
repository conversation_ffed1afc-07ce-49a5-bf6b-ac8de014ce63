// API Configuration
export const API_CONFIG = {
  BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:3001',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000 // 1 second
};

// File Upload Configuration
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: {
    EXCEL: ['.xlsx', '.xls'],
    CSV: ['.csv'],
    JSON: ['.json'],
    IMAGE: ['.png', '.jpg', '.jpeg', '.gif'],
    VIDEO: ['.mp4', '.avi', '.mov'],
    DOCUMENT: ['.pdf', '.doc', '.docx']
  },
  CHUNK_SIZE: 1024 * 1024 // 1MB chunks for large file uploads
};

// Pagination Configuration
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 25,
  PAGE_SIZE_OPTIONS: [10, 25, 50, 100],
  MAX_PAGE_SIZE: 1000
};

// Test Execution Configuration
export const TEST_EXECUTION = {
  MAX_PARALLEL_TESTS: 5,
  DEFAULT_TIMEOUT: 300000, // 5 minutes
  RETRY_ATTEMPTS: 3,
  SCREENSHOT_ON_FAILURE: true,
  VIDEO_RECORDING: false,
  BROWSER_OPTIONS: {
    HEADLESS: true,
    VIEWPORT: {
      WIDTH: 1920,
      HEIGHT: 1080
    }
  }
};

// Chart Configuration
export const CHARTS = {
  DEFAULT_COLORS: [
    '#1976d2', // Primary blue
    '#dc004e', // Secondary pink
    '#2e7d32', // Success green
    '#ed6c02', // Warning orange
    '#d32f2f', // Error red
    '#9c27b0', // Purple
    '#00796b', // Teal
    '#5d4037', // Brown
    '#455a64', // Blue grey
    '#e65100'  // Deep orange
  ],
  ANIMATION_DURATION: 300,
  RESPONSIVE_BREAKPOINTS: {
    MOBILE: 480,
    TABLET: 768,
    DESKTOP: 1024
  }
};

// Date and Time Configuration
export const DATE_TIME = {
  DEFAULT_FORMAT: 'yyyy-MM-dd HH:mm:ss',
  DATE_FORMAT: 'yyyy-MM-dd',
  TIME_FORMAT: 'HH:mm:ss',
  DISPLAY_FORMAT: 'MMM dd, yyyy HH:mm',
  TIMEZONE: 'UTC'
};

// Notification Configuration
export const NOTIFICATIONS = {
  DURATION: {
    SUCCESS: 3000,
    INFO: 4000,
    WARNING: 5000,
    ERROR: 6000
  },
  MAX_NOTIFICATIONS: 5,
  POSITION: 'top-right'
};

// WebSocket Configuration
export const WEBSOCKET = {
  RECONNECT_ATTEMPTS: 5,
  RECONNECT_DELAY: 3000,
  HEARTBEAT_INTERVAL: 30000,
  CONNECTION_TIMEOUT: 10000
};

// Cache Configuration
export const CACHE = {
  DEFAULT_TTL: 300000, // 5 minutes
  LONG_TTL: 3600000,   // 1 hour
  SHORT_TTL: 60000,    // 1 minute
  KEYS: {
    USER_PROFILE: 'user_profile',
    TEST_PROJECTS: 'test_projects',
    TEST_SUITES: 'test_suites',
    TEST_CASES: 'test_cases',
    REPORTS: 'reports'
  }
};

// UI Configuration
export const UI = {
  SIDEBAR_WIDTH: 280,
  SIDEBAR_COLLAPSED_WIDTH: 64,
  HEADER_HEIGHT: 64,
  FOOTER_HEIGHT: 48,
  DRAWER_WIDTH: 320,
  BREAKPOINTS: {
    XS: 0,
    SM: 600,
    MD: 960,
    LG: 1280,
    XL: 1920
  }
};

// Test Framework Configuration
export const TEST_FRAMEWORKS = {
  JEST: {
    name: 'Jest',
    extensions: ['.test.js', '.spec.js'],
    configFile: 'jest.config.js',
    command: 'npm test'
  },
  MOCHA: {
    name: 'Mocha',
    extensions: ['.test.js', '.spec.js'],
    configFile: 'mocha.opts',
    command: 'npm run test:mocha'
  },
  CYPRESS: {
    name: 'Cypress',
    extensions: ['.cy.js', '.spec.js'],
    configFile: 'cypress.config.js',
    command: 'npm run test:cypress'
  },
  PLAYWRIGHT: {
    name: 'Playwright',
    extensions: ['.test.js', '.spec.js'],
    configFile: 'playwright.config.js',
    command: 'npm run test:playwright'
  }
};

// Report Templates
export const REPORT_TEMPLATES = {
  EXECUTION_SUMMARY: {
    name: 'Test Execution Summary',
    description: 'Overview of test execution results',
    sections: ['summary', 'results', 'failures', 'timeline']
  },
  DETAILED_RESULTS: {
    name: 'Detailed Test Results',
    description: 'Comprehensive test results with logs',
    sections: ['summary', 'results', 'logs', 'screenshots', 'metrics']
  },
  TREND_ANALYSIS: {
    name: 'Trend Analysis',
    description: 'Historical trend analysis of test results',
    sections: ['trends', 'metrics', 'comparisons', 'insights']
  },
  COVERAGE_REPORT: {
    name: 'Test Coverage Report',
    description: 'Test coverage analysis and metrics',
    sections: ['coverage', 'uncovered', 'metrics', 'recommendations']
  }
};

// Data Export Formats
export const EXPORT_FORMATS = {
  CSV: {
    extension: '.csv',
    mimeType: 'text/csv',
    name: 'CSV'
  },
  EXCEL: {
    extension: '.xlsx',
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    name: 'Excel'
  },
  JSON: {
    extension: '.json',
    mimeType: 'application/json',
    name: 'JSON'
  },
  PDF: {
    extension: '.pdf',
    mimeType: 'application/pdf',
    name: 'PDF'
  },
  HTML: {
    extension: '.html',
    mimeType: 'text/html',
    name: 'HTML'
  }
};

// System Limits
export const SYSTEM_LIMITS = {
  MAX_TEST_CASES_PER_SUITE: 1000,
  MAX_TEST_SUITES_PER_PROJECT: 100,
  MAX_PROJECTS_PER_USER: 50,
  MAX_CONCURRENT_EXECUTIONS: 10,
  MAX_FILE_UPLOADS_PER_REQUEST: 5,
  MAX_BULK_OPERATIONS: 500,
  MAX_SEARCH_RESULTS: 1000
};

// Feature Flags
export const FEATURES = {
  ADVANCED_REPORTING: true,
  VIDEO_RECORDING: true,
  AI_TEST_GENERATION: false,
  INTEGRATION_JIRA: true,
  INTEGRATION_SLACK: true,
  INTEGRATION_TEAMS: false,
  PARALLEL_EXECUTION: true,
  SCHEDULED_EXECUTION: true,
  API_TESTING: true,
  PERFORMANCE_TESTING: false
};

// Environment Configuration
export const ENVIRONMENT = {
  DEVELOPMENT: 'development',
  STAGING: 'staging',
  PRODUCTION: 'production',
  TEST: 'test'
};

// Security Configuration
export const SECURITY = {
  PASSWORD_MIN_LENGTH: 6,
  PASSWORD_REQUIREMENTS: {
    LOWERCASE: true,
    UPPERCASE: true,
    NUMBERS: true,
    SPECIAL_CHARS: false
  },
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15 minutes
  CSRF_PROTECTION: true,
  XSS_PROTECTION: true
};

// Monitoring and Analytics
export const MONITORING = {
  ERROR_TRACKING: true,
  PERFORMANCE_MONITORING: true,
  USER_ANALYTICS: false,
  CRASH_REPORTING: true,
  LOG_RETENTION_DAYS: 30
};

// Integration URLs and Endpoints
export const INTEGRATIONS = {
  JIRA: {
    BASE_URL: 'https://your-domain.atlassian.net',
    API_VERSION: '3'
  },
  SLACK: {
    WEBHOOK_URL: process.env.SLACK_WEBHOOK_URL,
    CHANNEL: '#testing'
  },
  GITHUB: {
    API_URL: 'https://api.github.com',
    WEBHOOK_SECRET: process.env.GITHUB_WEBHOOK_SECRET
  }
};
