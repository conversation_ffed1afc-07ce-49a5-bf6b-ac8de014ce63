// User related types
export const UserRole = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  TESTER: 'tester',
  VIEWER: 'viewer'
};

export const UserStatus = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  SUSPENDED: 'suspended'
};

// Test Case related types
export const TestCaseType = {
  UNIT: 'unit',
  INTEGRATION: 'integration',
  FUNCTIONAL: 'functional',
  API: 'api',
  UI: 'ui',
  PERFORMANCE: 'performance'
};

export const TestCasePriority = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
};

export const TestCaseStatus = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  DEPRECATED: 'deprecated'
};

// Test Execution related types
export const ExecutionStatus = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
};

export const TestResultStatus = {
  PASS: 'pass',
  FAIL: 'fail',
  SKIP: 'skip',
  ERROR: 'error'
};

// Test Data related types
export const DataSetType = {
  CSV: 'csv',
  EXCEL: 'excel',
  JSON: 'json',
  MANUAL: 'manual'
};

// Report related types
export const ReportType = {
  EXECUTION: 'execution',
  COVERAGE: 'coverage',
  TREND: 'trend',
  CUSTOM: 'custom'
};

export const ReportFormat = {
  HTML: 'html',
  PDF: 'pdf',
  EXCEL: 'excel',
  JSON: 'json'
};

// File related types
export const FileType = {
  EXCEL: '.xlsx',
  EXCEL_OLD: '.xls',
  CSV: '.csv',
  JSON: '.json',
  PDF: '.pdf',
  IMAGE: ['.png', '.jpg', '.jpeg', '.gif'],
  VIDEO: ['.mp4', '.avi', '.mov']
};

// API Response types
export const ApiStatus = {
  SUCCESS: 'success',
  ERROR: 'error',
  LOADING: 'loading'
};

// Socket Event types
export const SocketEvents = {
  // Test execution events
  TEST_EXECUTION_STARTED: 'test_execution_started',
  TEST_EXECUTION_PROGRESS: 'test_execution_progress',
  TEST_EXECUTION_COMPLETED: 'test_execution_completed',
  TEST_EXECUTION_FAILED: 'test_execution_failed',
  
  // Test case events
  TEST_CASE_CREATED: 'test_case_created',
  TEST_CASE_UPDATED: 'test_case_updated',
  TEST_CASE_DELETED: 'test_case_deleted',
  
  // User events
  USER_JOINED: 'user_joined',
  USER_LEFT: 'user_left',
  
  // System events
  SYSTEM_NOTIFICATION: 'system_notification',
  ERROR_NOTIFICATION: 'error_notification'
};

// Validation schemas (using Joi-like structure for frontend)
export const ValidationSchemas = {
  user: {
    username: {
      required: true,
      minLength: 3,
      maxLength: 30,
      pattern: /^[a-zA-Z0-9_]+$/
    },
    email: {
      required: true,
      type: 'email'
    },
    password: {
      required: true,
      minLength: 6,
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/
    },
    firstName: {
      required: false,
      maxLength: 50
    },
    lastName: {
      required: false,
      maxLength: 50
    }
  },
  testCase: {
    title: {
      required: true,
      minLength: 1,
      maxLength: 255
    },
    description: {
      required: false,
      maxLength: 2000
    },
    type: {
      required: true,
      enum: Object.values(TestCaseType)
    },
    priority: {
      required: true,
      enum: Object.values(TestCasePriority)
    }
  },
  testSuite: {
    name: {
      required: true,
      minLength: 1,
      maxLength: 255
    },
    description: {
      required: false,
      maxLength: 1000
    }
  },
  testProject: {
    name: {
      required: true,
      minLength: 1,
      maxLength: 255
    },
    description: {
      required: false,
      maxLength: 1000
    }
  }
};

// Permission types
export const Permissions = {
  // User management
  USER_CREATE: 'user:create',
  USER_READ: 'user:read',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  
  // Project management
  PROJECT_CREATE: 'project:create',
  PROJECT_READ: 'project:read',
  PROJECT_UPDATE: 'project:update',
  PROJECT_DELETE: 'project:delete',
  
  // Test case management
  TEST_CASE_CREATE: 'test_case:create',
  TEST_CASE_READ: 'test_case:read',
  TEST_CASE_UPDATE: 'test_case:update',
  TEST_CASE_DELETE: 'test_case:delete',
  
  // Test execution
  TEST_EXECUTE: 'test:execute',
  TEST_EXECUTION_READ: 'test_execution:read',
  TEST_EXECUTION_DELETE: 'test_execution:delete',
  
  // Reports
  REPORT_CREATE: 'report:create',
  REPORT_READ: 'report:read',
  REPORT_DELETE: 'report:delete',
  
  // System administration
  SYSTEM_ADMIN: 'system:admin',
  SYSTEM_CONFIG: 'system:config'
};

// Role-based permissions mapping
export const RolePermissions = {
  [UserRole.ADMIN]: Object.values(Permissions),
  [UserRole.MANAGER]: [
    Permissions.USER_READ,
    Permissions.PROJECT_CREATE,
    Permissions.PROJECT_READ,
    Permissions.PROJECT_UPDATE,
    Permissions.PROJECT_DELETE,
    Permissions.TEST_CASE_CREATE,
    Permissions.TEST_CASE_READ,
    Permissions.TEST_CASE_UPDATE,
    Permissions.TEST_CASE_DELETE,
    Permissions.TEST_EXECUTE,
    Permissions.TEST_EXECUTION_READ,
    Permissions.TEST_EXECUTION_DELETE,
    Permissions.REPORT_CREATE,
    Permissions.REPORT_READ,
    Permissions.REPORT_DELETE
  ],
  [UserRole.TESTER]: [
    Permissions.PROJECT_READ,
    Permissions.TEST_CASE_CREATE,
    Permissions.TEST_CASE_READ,
    Permissions.TEST_CASE_UPDATE,
    Permissions.TEST_EXECUTE,
    Permissions.TEST_EXECUTION_READ,
    Permissions.REPORT_CREATE,
    Permissions.REPORT_READ
  ],
  [UserRole.VIEWER]: [
    Permissions.PROJECT_READ,
    Permissions.TEST_CASE_READ,
    Permissions.TEST_EXECUTION_READ,
    Permissions.REPORT_READ
  ]
};

// Error codes
export const ErrorCodes = {
  // Authentication errors
  TOKEN_MISSING: 'TOKEN_MISSING',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INVALID_TOKEN: 'INVALID_TOKEN',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  ACCOUNT_DEACTIVATED: 'ACCOUNT_DEACTIVATED',
  
  // Authorization errors
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  ACCESS_DENIED: 'ACCESS_DENIED',
  
  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  REQUIRED_FIELD_MISSING: 'REQUIRED_FIELD_MISSING',
  INVALID_FORMAT: 'INVALID_FORMAT',
  
  // Resource errors
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_ALREADY_EXISTS: 'RESOURCE_ALREADY_EXISTS',
  RESOURCE_IN_USE: 'RESOURCE_IN_USE',
  
  // File errors
  FILE_NOT_FOUND: 'FILE_NOT_FOUND',
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  FILE_PROCESSING_ERROR: 'FILE_PROCESSING_ERROR',
  
  // Database errors
  DATABASE_ERROR: 'DATABASE_ERROR',
  DUPLICATE_ENTRY: 'DUPLICATE_ENTRY',
  FOREIGN_KEY_CONSTRAINT: 'FOREIGN_KEY_CONSTRAINT',
  
  // Test execution errors
  TEST_EXECUTION_ERROR: 'TEST_EXECUTION_ERROR',
  TEST_TIMEOUT: 'TEST_TIMEOUT',
  BROWSER_ERROR: 'BROWSER_ERROR',
  
  // System errors
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED'
};
